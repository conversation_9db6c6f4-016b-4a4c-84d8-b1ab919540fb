<!--退费申请详情-->
<template>
  <div>
    <el-dialog
      :visible="true"
      width="1016px"
      top="30px"
      :before-close="back"
      title="退费申请详情"
      class="apply-detail"
      :append-to-body="true"
    >
      <div class="tg-dialog__content">
        <div class="stu-info">
          <div class="info tg-box--margin">
            <div class="info__content">
              <div class="info-box__border border-bottom-none">
                <span class="special-span--top">姓名</span>
                <span>{{ info.student_name }}</span>
              </div>
              <div class="info-box__border border-bottom-none">
                <span>学号</span>
                <span>{{ info.student_number }}</span>
              </div>
              <div class="info-box__border border-bottom-none">
                <span>所属校区</span>
                <span>{{ info.department_name }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-if="!businessTypeIsWallet" class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            ref="table"
            :data="tableData"
            tooltip-effect="dark"
            row-key="good_id"
            class="record-table"
            :tree-props="{ children: 'children' }"
          >
            <el-table-column type="index" label="编号" width="100">
            </el-table-column>
            <el-table-column
              label="项目名称"
              show-overflow-tooltip
              width="160"
              prop="good_name"
            >
              <template slot-scope="scope">
                <table-popover
                  v-if="scope.row.good_type === 'teach_aid_package'"
                  :goodsNum="scope.row.good_name"
                  :is_charge="true"
                  :goods="scope.row.goods"
                  :department_id="scope.row.department_id"
                  width="800"
                  :goodsId="scope.row.good_id"
                >
                </table-popover>
                <div v-else class="row__box--flex">
                  {{ scope.row.good_name }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="付费日期"
              width="110"
              prop="origin_order_at"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="收据号"
              width="100"
              prop="origin_receipt_id"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              label="单位"
              width="80"
              prop="unit"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              label="收款账户"
              width="100"
              prop="collect_account"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              label="购买单价"
              width="100"
              prop="purchased_single_price"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ scope.row.purchased_single_price.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column
              label="剩余数量"
              width="80"
              prop="number_left"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              label="剩余金额"
              width="100"
              prop=""
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{
                  (
                    scope.row.purchased_single_price * scope.row.number_left
                  ).toFixed(2)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="退费数量"
              width="80"
              prop="number"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              label="退赠送数量"
              width="100"
              prop="gift_number"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              label="退费金额"
              width="80"
              prop="refund_price"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.row.refund_price | roundNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="扣款金额"
              width="80"
              prop="expense"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.row.expense | roundNumber }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="row__box--flex">
          <div class="operation-info">
            <div class="info tg-box--margin">
              <h3>经办信息</h3>
              <div class="info__content">
                <div class="info-box__border">
                  <span class="special-span--top">开户人</span>
                  <span>{{ info.extend && info.extend.person_open }}</span>
                </div>
                <div class="info-box__border">
                  <span>开户银行</span>
                  <el-tooltip class="item" effect="dark" placement="top-start">
                    <span slot="content"
                      >{{ info.extend && info.extend.bank_open }}
                      {{
                        info.extend && info.extend.branch_name
                          ? "(" + info.extend.branch_name + ")"
                          : ""
                      }}</span
                    >
                    <span
                      >{{ info.extend && info.extend.bank_open }}
                      {{
                        info.extend && info.extend.branch_name
                          ? "(" + info.extend.branch_name + ")"
                          : ""
                      }}</span
                    >
                  </el-tooltip>
                </div>
                <div class="info-box__border">
                  <span>银行账号</span>
                  <span>{{ info.extend && info.extend.bank_account }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="refund-info">
            <div class="info tg-box--margin">
              <h3>退费信息</h3>
              <div class="info__content">
                <div class="info-box__border">
                  <span class="special-span--top"
                    >退费项目合计<el-tooltip
                      popper-class="tg-tooltip"
                      effect="light"
                      :content="`应退课程金额:${refundCourseAmount} 应退物品金额:${refundArticleAmount}`"
                      placement="top"
                    >
                      <img
                        class="tg-tooltip__icon"
                        src="@/assets/图片/icon_question.png"
                        alt=""
                      /> </el-tooltip
                  ></span>
                  <span>{{ info.refundable_price | roundNumber }}</span>
                </div>
                <div class="info-box__border">
                  <span>扣款金额</span>
                  <span>{{ info.expense | roundNumber }}</span>
                  <span></span>
                </div>
                <div class="info-box__border">
                  <span>退预存订金</span>
                  <span>{{ info.prepay_price | roundNumber }}</span>
                </div>
                <div class="info-box__border">
                  <span class="special-span--top">退费溢价</span>
                  <span>{{ info.refund_premium | roundNumber }}</span>
                </div>
                <div class="info-box__border border-bottom-none">
                  <span>退零钱金额</span>
                  <span>{{ info.wallet_price | roundNumber }}</span>
                </div>
                <div class="info-box__border border-bottom-none">
                  <span
                    >实退金额<el-tooltip
                      popper-class="tg-tooltip"
                      effect="light"
                      :content="`实退课程金额:${refundPriceCourseAmount} 实退物品金额:${refundPriceArticleAmount}`"
                      placement="top"
                    >
                      <img
                        class="tg-tooltip__icon"
                        src="@/assets/图片/icon_question.png"
                        alt=""
                      /> </el-tooltip
                  ></span>
                  <span>{{ info.refund_price | roundNumber }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="tg_approval-records">
          <div class="panel">
            <div class="title">处理详情</div>
            <div class="custom-table">
              <table>
                <thead>
                  <th>时间</th>
                  <th>操作人</th>
                  <th>操作</th>
                  <th style="width: 40%">备注</th>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in info.record" :key="index">
                    <td>
                      {{ item.record_at }}
                    </td>
                    <td>
                      {{ item.operator }}
                    </td>
                    <td>
                      {{ item.record }}
                    </td>
                    <td>
                      {{ item.remark }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div> -->
        <div style="margin-top: 16px" class="tg_box">
          <div class="tg_reason">
            <div class="title">退费原因及备注</div>
            <div class="content">
              <div>
                退费原因：{{ info?.extend?.reason_name }}
                <tempplate v-if="info?.extend?.sub_reason_name">
                  / {{ info?.extend?.sub_reason_name }}
                </tempplate>
              </div>
              <div style="margin-top: 10px; display: flex">
                <span style="width: 80px">申请备注：</span
                ><el-tooltip
                  v-if="info.extend.remark"
                  effect="light"
                  :content="info.extend.remark"
                  placement="top"
                >
                  <div
                    style="
                      -webkit-line-clamp: 2;
                      white-space: inherit;
                      display: -webkit-box;
                      -webkit-box-orient: vertical;
                    "
                    class="item__content tg_ellipsis"
                  >
                    {{ info.extend.remark }}
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
          <div class="tg_account">
            <div class="title">退款账户</div>
            <div class="content">
              <el-radio-group v-model="info.refund_channel">
                <div class="item">
                  <el-radio disabled label="transfer"
                    ><span>银行转账</span
                    ><span
                      >￥{{
                        (info.refund_channel == "transfer"
                          ? info.refund_price
                          : 0) | roundNumber
                      }}</span
                    ></el-radio
                  >
                </div>
                <div class="item">
                  <el-radio disabled label="cash"
                    ><span>虚拟退款</span>
                    <!-- <span></span> -->
                    <span
                      >￥{{
                        (info.refund_channel == "cash" ? info.refund_price : 0)
                          | roundNumber
                      }}</span
                    ></el-radio
                  >
                </div>
                <div class="item" v-if="isNetworkSchool">
                  <el-radio disabled label="back"
                    ><span>原路退回</span>
                    <!-- <span></span> -->
                    <span
                      >￥{{
                        (info.refund_channel == "back" ? info.refund_price : 0)
                          | roundNumber
                      }}</span
                    ></el-radio
                  >
                </div>
              </el-radio-group>
            </div>
          </div>
        </div>
        <div
          v-if="info.department_name === '聂卫平围棋网校-新'"
          style="margin-top: 16px"
          class="tg_box"
        >
          <div class="tg_reason">
            <div class="title">其他信息</div>
            <div class="content">
              <p class="other-p">
                <span>退费来源：</span>{{ info.extend.tw_remark }}
              </p>
              <p class="other-p">
                <span>业绩归属人：</span>
                {{ info.extend.performance | formatPerformanceUser }}
              </p>
              <p class="other-p">
                <span>上课次数：</span>{{ info.extend.class_num }}
              </p>
              <p class="other-p">
                <span>第三方交易单号：</span>{{ info.order_yop_id?.join() }}
              </p>
            </div>
          </div>
        </div>
        <div
          class="tg_box"
          v-if="
            info.status === 'unpaid' ||
            info.status === 'paid' ||
            info.status === 'discard'
          "
        >
          <div class="tg_reason" style="width: 100%">
            <div class="title">退款方式</div>
            <div class="content">
              <div
                v-if="info.status !== 'paid' && info.status !== 'discard'"
                class="pay-way-button-list"
              >
                <div
                  class="pay-way-button"
                  v-for="(item, index) in payWayOptions"
                  @click="selectPayWay(item)"
                  :class="{
                    'payway-button-selected': item.checked,
                    'payway-button-dis':
                      info.status === 'paid' || info.status === 'discard'
                  }"
                  :key="index"
                >
                  <div
                    class="tag_bg"
                    :style="`background:${item.color};`"
                  ></div>
                  <span>{{ item.value }}</span>
                  <span v-if="item.checked" class="tag_line">|</span>
                  <i
                    v-if="item.checked"
                    class="el-icon-close"
                    :style="`background-image:${
                      info.status === 'paid' || info.status === 'discard'
                        ? '@/assets/图片/关闭.png'
                        : '@/assets/图片/icon_close_white.png'
                    }`"
                    @click="delPayWay($event, item)"
                  ></i>
                </div>
              </div>
              <div class="payway-list">
                <div
                  v-for="(item, index) in payment"
                  :key="index"
                  class="payway-row"
                >
                  <span>{{ item.value }}</span>
                  <span>
                    <div class="custom--select">
                      <el-input
                        v-model.trim="item.pay_price"
                        @blur="payWayBlur(item)"
                        placeholder="请输入"
                        :disabled="
                          info.status === 'paid' || info.status === 'discard'
                        "
                        ><img
                          v-if="info.status === 'unpaid'"
                          src="@/assets/图片/icon_del_ac.png"
                          slot="suffix"
                          alt=""
                          class="del-suffix"
                          @click="removePayway(item)"
                        />
                      </el-input>
                    </div>
                  </span>
                  <!--如果是银行汇款方式-->
                  <template
                    v-if="
                      item.payment == 'transfer' ||
                      item.pay_method == 'transfer'
                    "
                  >
                    <span style="margin-right: 16px">付款银行账号</span>
                    <span
                      ><el-input
                        v-model="item.pay_account"
                        placeholder="付款银行账号"
                        :disabled="
                          info.status === 'paid' || info.status === 'discard'
                        "
                      ></el-input
                    ></span>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>

        <workflow-dialog
          v-if="workflow_dialog"
          :serial_number="info.serial_number"
          @close="workflow_dialog = false"
        ></workflow-dialog>

        <div
          class="attachment-preview"
          v-if="attachmentList && attachmentList.length"
        >
          <div class="item__label">图片附件</div>
          <div class="preview-imgs">
            <img
              v-for="(item, index) in attachmentList"
              :key="index"
              :src="item"
              preview="preview"
              preview-text=""
            />
          </div>
        </div>
        <div
          class="tg_down-btn"
          v-if="attachmentList && attachmentList.length"
          @click="downloadFile(attachmentList)"
        >
          下载附件
        </div>
        <el-button
          v-if="info.serial_number"
          style="margin-bottom: 16px"
          class="tg-button--plain"
          icon="el-icon-view"
          @click="workflow_dialog = true"
          type="plain"
          >查看审批流程明细</el-button
        >
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >关闭</el-button
        >
        <!-- 审批通过驳回 -->
        <template v-if="!readonly">
          <el-button
            v-if="info.status === 'unpaid'"
            class="tg-button--plain"
            type="plain"
            v-has="{ m: 'refund', o: 'cancel' }"
            @click="confirmReject()"
            >驳回</el-button
          >
        </template>
        <!-- 审批通过付款 -->
        <template v-if="!readonly">
          <el-button
            v-if="info.status === 'unpaid'"
            class="tg-button--primary"
            type="primary"
            v-has="{ m: 'refund', o: 'pay' }"
            @click="confrimPay"
            >确认付款</el-button
          >
          <el-button class="tg-button--primary" type="primary" @click="toPrint"
            >打印</el-button
          >
        </template>
      </span>
    </el-dialog>

    <!-- 确认付款弹出 -->
    <el-dialog
      :visible="payment_dialog_visible"
      width="800px"
      top="60px"
      title="确认付款"
      class="payment-box"
      :before-close="paymentClose"
      :append-to-body="true"
    >
      <el-form
        :model="paymentForm"
        ref="paymentForm"
        :rules="paymentRules"
        label-width="80px"
      >
        <el-form-item label="付款日期" required>
          <FinancialLockDate
            :departmentId="info.department_id"
            :date.sync="paymentForm.date"
            :disabledState="!chooseDate"
            placeholder="请选择指定日期"
          ></FinancialLockDate>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="paymentForm.remark"
            placeholder="请输入"
            type="textarea"
            clearable
            maxlength="255"
            class="payment-textarea"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="paymentClose"
          >取消</el-button
        >

        <el-button
          class="tg-button--primary"
          type="primary"
          v-throttle="confirmPayment"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <!-- 驳回弹出 -->
    <el-dialog
      :visible="reject_dialog_visible"
      width="800px"
      top="560px"
      title="驳回(必填)"
      class="reject-box"
      :before-close="rejectClose"
      :append-to-body="true"
    >
      <!-- <div class="reject-txt">请填写驳回意见</div> -->
      <div class="reject-textarea">
        <el-input
          v-model="rejectForm.remark"
          placeholder="请输入驳回原因(必填)"
          type="textarea"
          clearable
          maxlength="255"
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="rejectClose"
          >取消</el-button
        >

        <el-button
          class="tg-button--primary"
          type="primary"
          v-throttle="rejectConfirm"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <onlyRefundPrint ref="onlyRefundPrint" fromPage="refund"></onlyRefundPrint>
  </div>
</template>
<script>
import refundApi from "@/api/refund";

import onlyRefundPrint from "@/components/orderManagement/onlyRefundPrint.vue";
import WorkflowDialog from "./workflowDialog";
import systemSettingApi from "@/api/systemSetting";
import chargeApi from "@/api/charge";
import { roundNumber } from "@/public/utils";
export default {
  name: "ApplyDetail",
  props: {
    info: {
      type: Object,
      default: () => {}
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      refund_reason: [],
      reject_dialog_visible: false,
      chooseDate: false,
      payment_dialog_visible: false,
      workflow_dialog: false,
      attachmentList: [],
      ruleForm: {
        refundMethod: "transfer"
      },
      paymentForm: {
        date: "",
        remark: ""
      },
      rejectForm: {
        remark: ""
      },
      paymentRules: {},
      payWayOptions: [],
      payment: []
    };
  },
  components: {
    onlyRefundPrint,
    WorkflowDialog
  },
  computed: {
    // 是否是订金退费
    businessTypeIsWallet() {
      if (this.info && this.info.items) {
        return this.info.items.some((item) => item.add_type === "wallet");
      }
      return false;
    },
    isNetworkSchool() {
      return ["网校测试校区C", "聂卫平围棋网校-新"].includes(
        this.info.department_name
      );
    },
    departmentId() {
      return this.$store.getters.doneGetSchoolId;
    },
    // ----还差金额
    surplus_amount() {
      const { payment } = this;
      let sum = 0;
      if (payment) {
        payment.map((item) => {
          if (item.pay_price) {
            sum += Number(item.pay_price);
          }
        });
      }
      return roundNumber(
        roundNumber(this.info.refund_price) - roundNumber(sum)
      );
    },
    // 计算物品应退费金额
    refundArticleAmount() {
      const { items } = this.info;
      if (!items) return 0;
      const articleAmount = items.reduce((acc, item) => {
        if (item.good_type === "article") {
          return acc + item.refund_price;
        }
        return acc;
      }, 0);
      return roundNumber(articleAmount);
    },
    // 计算课程应退费金额
    refundCourseAmount() {
      const { items } = this.info;
      if (!items) return 0;
      const courseAmount = items.reduce((acc, item) => {
        if (item.good_type === "course") {
          return acc + item.refund_price;
        }
        return acc;
      }, 0);
      return roundNumber(courseAmount);
    },
    // 计算物品实退费金额 refund_price - expense
    refundPriceArticleAmount() {
      const { items } = this.info;
      if (!items) return 0;
      const articleAmount = items.reduce((acc, item) => {
        if (item.good_type === "article") {
          return acc + (item.refund_price - item.expense);
        }
        return acc;
      }, 0);
      return roundNumber(articleAmount);
    },
    // 计算课程实退费金额 refund_price - expense
    refundPriceCourseAmount() {
      const { items } = this.info;
      if (!items) return 0;
      const courseAmount = items.reduce((acc, item) => {
        if (item.good_type === "course") {
          return acc + (item.refund_price - item.expense);
        }
        return acc;
      }, 0);
      return roundNumber(courseAmount);
    }
  },
  mounted() {
    this.getRefundTransferReason();
    this.chooseDate = this.$_has({ m: "refund", o: "chooseDate" });
    this.set_apply_list();
    this.getPaymentMethod();

    const parent_arr = [];
    this.info.items.map((parent_row) => {
      const webViewToast = parent_row;
      if (webViewToast?.pid === "") {
        parent_arr.push(parent_row);
      }
    });

    // this.info.items.map((parent_row) => {
    //   const webViewToast = parent_row;
    //   // parent_arr.push(webViewToast);
    //   if (webViewToast.good_type === "teach_aid_package") {
    //     if (webViewToast?.pid === "") {
    //       parent_arr.push(parent_row);
    //     }
    //   } else {
    //     parent_arr.push(webViewToast);
    //   }
    // });
    parent_arr.map((item) => {
      const key = item.good_type === "teach_aid_package" ? "goods" : "children";
      this.info.items.map((parent_row) => {
        if (parent_row.pid) {
          if (item.good_id === parent_row.pid) {
            if (item[key]) {
              item[key].push(parent_row);
            } else {
              item[key] = [];
              item[key].push(parent_row);
            }
          }
        }
      });
    });

    console.log(parent_arr, "table_list");
    this.tableData = parent_arr;
  },
  filters: {
    formatPerformanceUser(val) {
      if (val) {
        const arr = [];
        if (val.length) {
          val.map((item) => {
            const { employee_name, role_name } = item;
            arr.push(`${employee_name}(${role_name})`);
          });
          return arr.join("、");
        } else {
          return "";
        }
      }
      return "";
    },
    deductionItemAmountFilter(val) {
      const {
        confirmFee,
        cardFee,
        dataFee,
        handlingFee,
        manageFee,
        offlineFee
      } = val;
      if (val) {
        const sum =
          confirmFee + cardFee + dataFee + handlingFee + manageFee + offlineFee;
        return sum.toFixed(2);
      }
      return "";
    },
    // refundReasonFilter(val) {
    //   if (val) {
    //     const arr = refund_reason.filter((item) => item.id === val);
    //     return arr[0].label;
    //   }
    //   return "";
    // },
    unitFilter(val) {
      if (val) {
        const { unit } = JSON.parse(val);
        return unit;
      }
      return "";
    }
  },
  methods: {
    fillPayment(payment) {
      const { payWayOptions } = this;
      console.log(payment);
      console.log(payWayOptions);
      payment?.forEach((item1) => {
        item1.pay_price = item1.price;
        const checkedItem = payWayOptions.find(
          (item2) => item1.payment === item2.key
        );
        checkedItem.checked = true;
        item1.value = checkedItem.value;
        item1.type = checkedItem.type;
      });
      this.payment = payment ?? [];
    },

    get_payment_param() {
      this.payment.forEach((item) => {
        item.payment = item.pay_method;
        item.price = item.pay_price;
      });
      return this.payment.filter(
        (item) =>
          item.price !== "" && item.price !== undefined && item.price !== null
      );
    },

    async getPaymentMethod(d) {
      const res = await chargeApi.paymentMethod({
        department_id: this.departmentId
      });
      const { data } = res.data;
      this.payWayOptions = data.filter(
        (item) =>
          item.key === "cash" ||
          item.key === "origin_back" ||
          item.key === "transfer" ||
          item.key === "third_delay" ||
          item.key === "transfer_cross_campus"
      );
      this.payWayOptions = this.payWayOptions.filter((i) => {
        if (this.isNetworkSchool) {
          return true;
        } else {
          return i.key !== "origin_back";
        }
      });
      console.log(this.payWayOptions, this.info.department_name);
      if (this.info.payment) {
        this.fillPayment(this.info.payment);
      }
    },
    delPayWay(e, val) {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      } else {
        e.stopPropagation();
        const index = this.payment.findIndex((item) => {
          return item.pay_method === val.key;
        });
        this.payment.splice(index, 1);

        val.checked = false;
        this.$forceUpdate();
      }
    },
    removePayway(val) {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      } else {
        this.payment.splice(
          this.payment.findIndex((item) => item.pay_method === val.pay_method),
          1
        );
        this.payWayOptions.map((item) => {
          if (item.key === val.pay_method) {
            item.checked = false;
          }
        });
        this.$forceUpdate();
      }
    },

    selectPayWay(val) {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      } else {
        if (val.type === "online") {
          const itemIndex = this.payment.findIndex((item) => {
            return item.type === "online";
          });
          if (itemIndex !== -1) {
            this.payment.splice(itemIndex, 1);
            this.payWayOptions.map((item) => {
              if (item.type === "online") item.checked = false;
            });
          }
        }
        val.checked = true;
        const index = this.payment.findIndex((item) => {
          return item.pay_method === val.key;
        });
        if (index < 0) {
          this.payment.push({
            pay_method: val.key,
            type: val.type,
            value: val.value
          });
        }
      }
    },

    payWayBlur(item) {
      const val = item.pay_price;
      if (val) {
        if (!/^[0-9]+(.[0-9]{1,2})?$/.test(val)) {
          this.$message.info("输入数据格式有误，请重新输入！");
          item.pay_price = "";
        } else {
          item.pay_price = Number(item.pay_price);
        }
      }
    },

    async getRefundTransferReason() {
      const { data } = await systemSettingApi.refundTransferReason({
        department_id: this.departmentId,
        name: ["退费原因设置"]
      });
      if (data.message === "success") {
        this.refund_reason = data.data ?? [];
      }
    },
    refundTwRemarkFilter(val) {
      if (val) {
        const arr = this.refund_reason?.filter((item) => item.id === val);
        return arr[0]?.name;
      }
      return "";
    },
    confirmReject() {
      if (!["paid", "cancel"].includes(this.info.status)) {
        this.reject_dialog_visible = true;
      }
    },
    // 驳回确认
    rejectConfirm() {
      if (!this.rejectForm.remark) {
        this.$message.info("请填写驳回原因!");
        return;
      }
      const data = {
        remark: this.rejectForm.remark,
        refund_id: this.info.refund_id
      };
      refundApi.refundCancel(data).then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          this.$message.success("驳回成功!");
          this.reject_dialog_visible = false;
          this.$emit("refresh");
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    rejectClose() {
      this.reject_dialog_visible = false;
    },
    // 校验银行卡格式
    validateBankNo() {
      const { payment } = this;
      const transfer = payment.find((item) => item.pay_method === "transfer");
      if (transfer && !transfer.pay_account) {
        this.$message.info("付款银行卡号不能为空！");
        return false;
      } else {
        return true;
      }
    },
    confrimPay() {
      if (!["paid", "cancel"].includes(this.info.status)) {
        if (!this.validateBankNo()) {
          return;
        }
        // if (this.surplus_amount >= 0.01) {
        //   this.$message.info(
        //     `还差金额${this.surplus_amount}元，不能进行退费！`
        //   );
        //   return;
        // }
        // if (this.surplus_amount <= -0.01) {
        //   this.$message.info(
        //     `还差金额${this.surplus_amount}元，不能进行退费！`
        //   );
        //   return;
        // }
        if (this.surplus_amount > 0 || this.surplus_amount < 0) {
          this.$message.info(
            `还差金额${this.surplus_amount}元，不能进行退费！`
          );
          return;
        }
        // if (this.surplus_amount < 0.01 && this.surplus_amount > 0) {
        //   this.payment[0].pay_price += this.surplus_amount;
        // }
        this.payment_dialog_visible = true;
        this.paymentForm.date = this.moment().format("YYYY-MM-DD");
      }
    },
    set_apply_list() {
      this.attachmentList = this.info?.extend?.attachment ?? [];
    },
    // 确认付款
    confirmPayment() {
      console.log(this.surplus_amount);

      if (!this.paymentForm.date) {
        this.$message.error("请选择付款日期!");
        return;
      }

      const data = {
        remark: this.paymentForm.remark,
        refund_id: this.info.refund_id,
        pay_time: this.paymentForm.date,
        payment:
          this.info.status === "unpaid" ||
          this.info.status === "paid" ||
          this.info.status === "discard"
            ? this.get_payment_param()
            : undefined
      };
      refundApi.refundPay(data).then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          this.$message.success("付款成功!");
          this.payment_dialog_visible = false;
          this.$emit("refresh");
        } else {
          this.$message.error(res.data.message);
        }
      });
    },

    paymentClose() {
      this.payment_dialog_visible = false;
    },
    back() {
      this.$emit("close");
    },

    toPrint() {
      // 打印收据
      this.$refs.onlyRefundPrint.openDialog();
    },
    downloadFile(row) {
      if (row && row.length) {
        row.forEach((item) => {
          window.open(item);
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.apply-detail {
  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
  }

  .tg-dialog__content {
    overflow: hidden;
    min-height: 700px;
  }

  ::v-deep .record-table {
    padding: 0;
    width: 100%;
    margin: 0;

    th {
      background: #f5f8fc;
      height: 40px;
    }

    th:nth-child(1),
    td:nth-child(1) {
      .cell {
        padding-left: 42px;
      }
    }
  }

  .tg-box--border {
    height: 38px;
  }

  ::v-deep .tg-table__box {
    margin: 0;
    margin-top: 16px;
    // box-shadow: 0 2px 6px 0 rgb(204 208 217 / 35%);
    overflow: hidden;

    .el-table__body-wrapper {
      border: 1px solid #e0e6ed;
      box-sizing: border-box;
      // overflow: hidden;
      border-top: 0;
      border-bottom: 0;
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
    }
  }

  .title {
    padding-top: 16px;
  }

  ::v-deep .el-table::before {
    height: 0;
  }

  .row__box--flex {
    display: flex;
    justify-content: space-between;

    .info {
      border: 1px solid @base-color;
      border-radius: 4px;
      overflow: hidden;

      & > h3 {
        font-size: 14px;
        font-weight: 500;
        padding-left: 16px;
        background-color: #f5f8fc;
        display: block;
        line-height: 48px;
        margin: 0;
        border-bottom: 1px solid @base-color;
      }
    }

    .operation-info {
      width: 484px;

      .info__content {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        & > div {
          width: 100%;
          display: flex;
          align-items: center;
        }

        & > div.info-box__border--special {
          width: 100%;
        }

        span {
          font-size: @text-size_normal;
          color: @text-color_second;
          font-family: @text-famliy_medium;
          line-height: 46px;
        }

        span:nth-child(1) {
          background-color: #f5f8fc;
          min-width: 90px;
          display: inline-block;
          text-align: center;
          padding: 0 16.5px;
        }

        .info-box__border:nth-child(1) {
          span:nth-child(1) {
            border-bottom-left-radius: 4px;
          }
        }

        span:nth-child(2) {
          width: calc(100% - 132px);
          padding-left: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        span.tg-text--blue {
          color: #157df0;
        }

        .special-span--top {
          border-top-left-radius: 4px;
        }
      }
    }

    .refund-info {
      width: 484px;

      .info__content {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        & > div {
          width: 50%;
          display: flex;
          align-items: center;
        }

        & > div.info-box__border--special {
          width: 100%;
        }

        span {
          font-size: @text-size_normal;
          color: @text-color_second;
          font-family: @text-famliy_medium;
          line-height: 46px;
        }

        span:nth-child(1) {
          background-color: #f5f8fc;
          width: 150px;
          display: inline-block;
          text-align: center;
          padding: 0 16.5px;
        }

        .info-box__border:nth-child(1) {
          span:nth-child(1) {
            border-bottom-left-radius: 4px;
          }
        }

        span:nth-child(2) {
          width: calc(100% - 132px);
          padding-left: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        span.tg-text--blue {
          color: #157df0;
        }

        .special-span--top {
          border-top-left-radius: 4px;
        }
      }
    }
  }

  .stu-info {
    .info {
      border: 1px solid @base-color;
      border-radius: 4px;
      overflow: hidden;
    }

    .info__content {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      & > div {
        width: 33.333%;
        display: flex;
        align-items: center;
      }

      & > div.info-box__border--special {
        width: 100%;
      }

      span {
        font-size: @text-size_normal;
        color: @text-color_second;
        font-family: @text-famliy_medium;
        line-height: 46px;
      }

      span:nth-child(1) {
        background-color: #f5f8fc;
        min-width: 57px;
        display: inline-block;
        text-align: center;
        padding: 0 16.5px;
      }

      .info-box__border:nth-child(1) {
        span:nth-child(1) {
          border-bottom-left-radius: 4px;
        }
      }

      span:nth-child(2) {
        width: calc(100% - 132px);
        padding-left: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      span.tg-text--blue {
        color: #157df0;
      }

      .special-span--top {
        border-top-left-radius: 4px;
      }
    }
  }

  .info-box__border {
    box-sizing: border-box;
    border-bottom: 1px solid #e0e6ed;

    // border-right: 1px solid #e0e6ed;
    &:last-child {
      border-right: 0;
    }

    &.border-bottom-none {
      border-bottom: 0px;
    }
  }
  .tg_approval-records {
    margin: 17px 0;
    .panel {
      border: 1px solid #2d80ed;
      border-radius: 4px;
      overflow: hidden;
      .title {
        padding: 0;
        font-size: 14px;
        padding-left: 16px;
        line-height: 46px;
        color: #1f2d3d;
        background-color: #f5f8fc;
        font-weight: 500;
      }
      .custom-table {
        table {
          box-sizing: border-box;
          width: 100%;
          border-collapse: collapse;
          border-spacing: 0;
        }
        thead {
          border-top: 1px solid #2d80ed;
          font-size: 13px;
          height: 46px;
          background-color: #ebf4ff;
          color: #475669;
        }
        tbody td {
          font-size: 13px;
          height: 46px;
          text-align: center;
          border-bottom: 1px solid #e0e6ed;
          color: #475669;
        }
        tbody tr:last-child td {
          border-bottom: 0;
        }
      }
    }
  }
  .tg_box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .title {
      border: 1px solid #2d80ed;
      font-size: 14px;
      padding-left: 16px;
      line-height: 46px;
      color: #1f2d3d;
      background-color: #f5f8fc;
      font-weight: 500;
      padding-top: 0;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
    }
    .content {
      border: 1px solid #e0e6ed;
      border-top: 0px;
      border-bottom-right-radius: 4px;
      border-bottom-left-radius: 4px;
      padding: 16px;
      min-height: 102px;
      box-sizing: border-box;
    }
    .tg_reason {
      width: 655px;
      .other-p {
        margin-bottom: 10px;
        span {
          width: 115px;
          text-align: right;
          display: inline-block;
        }
      }
    }
    .tg_account {
      width: 313px;
      .content {
        .item {
          display: flex;
          justify-content: space-between;
        }
        .item:nth-child(1) {
          margin-bottom: 16px;
        }
        .item:nth-child(2) {
          margin-bottom: 16px;
        }
      }
      /deep/ .el-radio-group {
        width: 100%;
        .el-radio {
          display: flex;
          width: 100%;
          .el-radio__label {
            display: flex;
            flex: 1;
            justify-content: space-between;
          }
        }
      }
    }
  }
  .tg_down-btn {
    color: #2d80ed;
    padding-bottom: 20px;
    cursor: pointer;
  }
}
.reject-box {
  /deep/ .el-dialog__body {
    padding: 16px;
    .reject-txt {
      font-size: 12px;
      color: #8492a6;
      margin-bottom: 16px;
    }
    .reject-textarea {
      textarea {
        height: 110px;
      }
    }
  }
}
.payment-box {
  /deep/ .el-dialog__body {
    padding: 16px;
    .el-form-item {
      margin-bottom: 16px;
      .el-date-editor {
        height: 32px;
        line-height: 32px;
        width: 324px;
      }
    }

    .payment-textarea {
      textarea {
        height: 128px;
        width: 560px;
      }
    }
  }
}
.attachment-preview {
  font-size: 13px;
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  .item__label {
    margin: 16px 0;
  }
  .preview-imgs {
    img {
      width: 140px;
      height: 140px;
      background-color: #f3f3f3;
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;
    }
  }
}
.pay-way-button-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 16px;
  .pay-way-button {
    cursor: pointer;
    height: 32px;
    min-width: 96px;
    margin-bottom: 10px;
    // position: relative;
    text-align: center;
    // line-height: 32px;
    border-radius: 4px;
    margin-right: 20px;
    box-sizing: border-box;
    padding: 0 12px;
    font-size: 14px;
    background: #fff;
    color: #2d80ed;
    border: 1px solid #2d80ed;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    /deep/ .el-icon-close {
      // background-image: url("~@/assets/图片/icon_close_white.png");
      width: 10px;
      height: 10px;
    }
    &:hover {
      background: #ebf4ff;
      color: #2d80ed;
      .el-icon-close {
        background-image: url("~@/assets/图片/icon_close.png");
      }
    }
    // .tag_bg {
    //   width: 100%;
    //   height: 100%;
    //   border-radius: 4px;
    //   opacity: 0.12;
    //   position: absolute;
    //   left: 0;
    //   top: 0;
    // }
    .tag_line {
      padding: 0 8px;
    }

    // /deep/.el-icon-close {
    //   border-radius: 50%;
    //   text-align: center;
    //   position: relative;
    //   cursor: pointer;
    //   font-size: 12px;
    //   height: 16px;
    //   line-height: 16px;
    //   vertical-align: middle;
    //   top: -1px;
    //   background-image: none;
    //   &:hover {
    //     background-image: none;
    //   }
    // }
  }
  .payway-button-selected {
    background: #2d80ed;
    color: #fff;
  }
  .payway-button-dis {
    background: #f5f7fa;
    color: #b3b7c6;
    border: none;
    pointer-events: none;
  }
}
.payway-list {
  display: flex;
  width: 100%;
  align-items: center;
  flex-wrap: wrap;
  .payway-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    margin-right: 16px;
    width: 100%;
    span:nth-child(1) {
      width: 100px;
      margin-right: 12px;
    }

    span:nth-child(2) {
      text-align: left;
    }

    span:nth-child(3) {
      margin-left: 16px;
    }
  }
}
.del-suffix {
  width: 14px;
  height: 14px;
  margin-top: 6px;
  cursor: pointer;
}
::v-deep .tg-tooltip__icon {
  width: 14px;
  vertical-align: initial;
  margin: -3px;
  margin-left: 10px;
}
</style>
