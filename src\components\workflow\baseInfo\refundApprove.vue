<template>
  <div class="content">
    <div class="detail content-bottom">
      <div class="item">
        <span class="item__label">申请编号</span>
        <span class="item__content">{{ approve_info.serial_number }}</span>
      </div>
      <div class="item">
        <span class="item__label">审批状态</span>
        <span class="item__content">{{
          approve_info.status | getStatusCn
          }}</span>
      </div>
      <div class="item">
        <span class="item__label">业务类型</span>
        <span class="item__content">{{ approve_info.template_name }}</span>
      </div>
      <div class="item">
        <span class="item__label">校区</span>
        <span class="item__content">{{ basic_info.department_name }}</span>
      </div>
      <div class="item">
        <span class="item__label">学员姓名</span>
        <span class="item__content">{{ basic_info.student_name }}</span>
      </div>
      <div class="item">
        <span class="item__label">学号</span>
        <span class="item__content">{{ basic_info.student_number }}</span>
      </div>
      <div class="item">
        <span class="item__label">申请时间</span>
        <span class="item__content">{{ basic_info.created_at | getTime }}</span>
      </div>
      <div class="item">
        <span class="item__label">经手人</span>
        <span class="item__content">{{ basic_info.operator_name }}</span>
      </div>
      <div class="item">
        <span class="item__label">应退金额</span>
        <el-tooltip effect="light" :content="`课程：${basic_info.should_refund.course_refund_price.toFixed(
          2
        )} 物品：${basic_info.should_refund.article_refund_price.toFixed(2)}`" placement="top">
          <span class="item__content">{{ basic_info.refundable_price.toFixed(2)
          }}<span>{{
              `（课程：${basic_info.should_refund.course_refund_price.toFixed(
                2
              )} 物品：${basic_info.should_refund.article_refund_price.toFixed(
                2
              )}）`
            }}</span></span>
        </el-tooltip>
      </div>
      <div class="item">
        <span class="item__label">实际退费</span>
        <!-- <span class="item__content">{{
          basic_info.refund_price.toFixed(2)
        }}</span> -->
        <el-tooltip effect="light" :content="`课程：${basic_info.actual_refund.course_refund_price.toFixed(
          2
        )} 物品：${basic_info.actual_refund.article_refund_price.toFixed(2)}`" placement="top">
          <span class="item__content">{{ basic_info.refund_price.toFixed(2)
          }}<span>{{
              `（课程：${basic_info.actual_refund.course_refund_price.toFixed(
                2
              )} 物品：${basic_info.actual_refund.article_refund_price.toFixed(
                2
              )}）`
            }}</span></span>
        </el-tooltip>
      </div>
      <div class="item">
        <span class="item__label">退费原因</span>
        <el-tooltip effect="light" :content="`${basic_info.reason}${basic_info.sub_reason ? ' / ' + basic_info.sub_reason : ''
          }`" placement="top">
          <span class="item__content">
            {{ basic_info.reason }}
            <template v-if="basic_info.sub_reason">
              / {{ basic_info.sub_reason }}
            </template>
          </span>
        </el-tooltip>
      </div>
      <div class="item">
        <span class="item__label">申请备注</span>
        <el-tooltip effect="light" :content="basic_info.remark" placement="top">
          <span class="item__content">{{ basic_info.remark }}</span>
        </el-tooltip>
      </div>
      <div class="item">
        <span class="item__label">开户人</span>
        <span class="item__content">{{ basic_info.account_holder }}</span>
      </div>
      <div class="item">
        <span class="item__label">开户行</span>
        <el-tooltip effect="light" :content="basic_info.bank_name" placement="top">
          <span class="item__content">{{ basic_info.bank_name }}</span>
        </el-tooltip>
      </div>
      <div class="item">
        <span class="item__label">银行账号</span>
        <el-tooltip effect="light" :content="basic_info.bank_account" placement="top">
          <span class="item__content">{{ basic_info.bank_account }}</span>
        </el-tooltip>
      </div>
      <div class="item">
        <span class="item__label">合思主体</span>
        <el-tooltip effect="light" :content="basic_info.hose_entity" placement="top">
          <span class="item__content">{{ basic_info.hose_entity }}</span>
        </el-tooltip>
      </div>
      <template v-if="basic_info.department_name === '聂卫平围棋网校-新'">
        <div class="item">
          <span class="item__label">退款方式</span>
          <el-tooltip effect="light" :content="refundChannels[basic_info.refund_channel]" placement="top">
            <span class="item__content">{{
              refundChannels[basic_info.refund_channel]
              }}</span>
          </el-tooltip>
        </div>
        <div class="item">
          <span class="item__label">第三方交易单号</span>
          <el-tooltip effect="light" :content="basic_info.order_yop_id?.join()" placement="top">
            <span class="item__content">{{
              basic_info.order_yop_id?.join()
              }}</span>
          </el-tooltip>
        </div>
        <div class="item">
          <span class="item__label">退费来源</span>
          <span class="item__content">{{ basic_info.tw_remark }}</span>
        </div>
      </template>
    </div>
    <div class="attachment-preview" v-if="basic_info.attachment && basic_info.attachment.length">
      <div class="item__label">图片附件</div>
      <div class="preview-imgs">
        <img v-for="(item, index) in basic_info.attachment" :key="index" :src="item" preview="preview"
          preview-text="" />
      </div>
    </div>
    <div style="padding-top: 16px">
      <el-button @click="toDetail" type="text" size="small" class="tg-text--blue">查看明细</el-button>
    </div>
    <apply-detail v-if="apply_detail_visible" :info="detailInfo" :readonly="true"
      @close="apply_detail_visible = false"></apply-detail>
  </div>
</template>
<script>
import refundApi from "@/api/refund";
import { base_info_components_name, workflow_status } from "@/public/dict";
import { downLoadFileByHref } from "@/public/downLoadFile";
import ApplyDetail from "@/views/refund/applyDetail.vue";
export default {
  name: base_info_components_name.refund,
  data() {
    return {
      apply_detail_visible: false,
      refundChannels: {
        back: "原路退回",
        cash: "虚拟退款",
        transfer: "银行转账"
      },
      detailInfo: {}
    };
  },
  props: {
    approve_info: Object,
    basic_info: Object,
    promoter_info: Object
  },
  components: {
    ApplyDetail
  },
  filters: {
    getStatusCn(val) {
      const item = workflow_status.find((item) => val === item.id);
      return typeof item === "undefined" ? "" : item.name;
    }
  },
  methods: {
    downloadFile(files) {
      downLoadFileByHref(files);
    },
    // 查看详情
    toDetail() {
      refundApi
        .getRefundApplyDetail({
          order_id: this.basic_info.refund_id,
          with_del: false
        })
        .then((res) => {
          this.detailInfo = res.data.data;
          this.detailInfo.status = "unpaid";
          this.apply_detail_visible = true;
        });
    }
  }
};
</script>
<style lang="less" scoped>
@import "~@/assets/workflow/css/detailInfo.less";

/deep/ .detail .item .item__content.tg_down-btn {
  color: #2d80ed;
  cursor: pointer;
}

.attachment-preview {
  font-size: 13px;
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  color: #8492a6;

  .item__label {
    margin: 16px 0;
  }

  .preview-imgs {
    img {
      width: 140px;
      height: 140px;
      background-color: #f3f3f3;
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;
    }
  }
}
</style>
